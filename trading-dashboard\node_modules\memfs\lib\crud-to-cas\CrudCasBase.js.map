{"version": 3, "file": "CrudCasBase.js", "sourceRoot": "", "sources": ["../../src/crud-to-cas/CrudCasBase.ts"], "names": [], "mappings": ";;;AAIA,MAAM,eAAe,GAAG,KAAK,EAAK,IAAsB,EAAc,EAAE;IACtE,IAAI,CAAC;QACH,OAAO,MAAM,IAAI,EAAE,CAAC;IACtB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,oBAAoB;oBACvB,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAEF,MAAa,WAAW;IACtB,YACqB,IAAa,EACb,IAAyC,EACzC,QAAoC,EACpC,SAA0C;QAH1C,SAAI,GAAJ,IAAI,CAAS;QACb,SAAI,GAAJ,IAAI,CAAqC;QACzC,aAAQ,GAAR,QAAQ,CAA4B;QACpC,cAAS,GAAT,SAAS,CAAiC;QAG/C,QAAG,GAAG,KAAK,EAAE,IAAgB,EAAiB,EAAE;YAC9D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAChD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;QAEc,QAAG,GAAG,KAAK,EAAE,IAAU,EAAE,OAAuB,EAAuB,EAAE;YACvF,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACnD,OAAO,MAAM,eAAe,CAAC,KAAK,IAAI,EAAE;gBACtC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBACvD,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,CAAA,EAAE,CAAC;oBAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACrC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC;wBAAE,MAAM,IAAI,YAAY,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC;gBAC9G,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEc,QAAG,GAAG,KAAK,EAAE,IAAU,EAAE,MAAgB,EAAiB,EAAE;YAC1E,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,eAAe,CAAC,KAAK,IAAI,EAAE;gBAC/B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEc,SAAI,GAAG,KAAK,EAAE,IAAU,EAA6B,EAAE;YACrE,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACnD,OAAO,MAAM,eAAe,CAAC,KAAK,IAAI,EAAE;gBACtC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IAjCC,CAAC;CAkCL;AAxCD,kCAwCC"}