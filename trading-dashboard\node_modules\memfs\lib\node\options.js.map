{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../../src/node/options.ts"], "names": [], "mappings": ";;;AAmBA,gCAoBC;AAED,sCAEC;AAED,gDAGC;AA/CD,2CAA0C;AAC1C,0CAA6C;AAE7C,iCAA0C;AAG1C,MAAM,aAAa,GAAuB;IACxC,IAAI,oBAAU;IACd,SAAS,EAAE,KAAK;CACjB,CAAC;AAEK,MAAM,eAAe,GAAG,CAAC,OAAO,EAAsB,EAAE;IAC7D,IAAI,OAAO,OAAO,KAAK,QAAQ;QAAE,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;IAC5F,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;AACnD,CAAC,CAAC;AAHW,QAAA,eAAe,mBAG1B;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,EAAE,CAAC,gEAAgE,MAAM,UAAU,CAAC;AAE/G,SAAgB,UAAU,CAA0B,QAAW,EAAE,OAAoB;IACnF,IAAI,IAAO,CAAC;IACZ,IAAI,CAAC,OAAO;QAAE,OAAO,QAAQ,CAAC;SACzB,CAAC;QACJ,MAAM,MAAM,GAAG,OAAO,OAAO,CAAC;QAC9B,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,QAAQ;gBACX,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAiB,EAAE,CAAC,CAAC;gBACpE,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC5C,MAAM;YACR;gBACE,MAAM,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ;QAAE,IAAA,yBAAc,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAE9D,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,aAAa,CAAQ,QAAe;IAClD,OAAO,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAClD,CAAC;AAED,SAAgB,kBAAkB,CAAiB,OAAO;IACxD,OAAO,CAAC,OAAO,EAAE,QAAS,EAAE,EAAE,CAC5B,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,IAAA,uBAAgB,EAAC,QAAQ,CAAC,CAAC,CAAC;AAC1G,CAAC;AAEY,QAAA,YAAY,GAAkB;IACzC,QAAQ,EAAE,MAAM;CACjB,CAAC;AAEW,QAAA,cAAc,GAAG,aAAa,CAAgB,oBAAY,CAAC,CAAC;AAC5D,QAAA,mBAAmB,GAAG,kBAAkB,CAAqB,sBAAc,CAAC,CAAC;AAE1F,MAAM,aAAa,GAAuB;IACxC,SAAS,EAAE,KAAK;CACjB,CAAC;AAEK,MAAM,eAAe,GAAG,CAAC,OAAO,EAAsB,EAAE;IAC7D,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;AACnD,CAAC,CAAC;AAFW,QAAA,eAAe,mBAE1B;AAEF,MAAM,SAAS,GAAG,aAAa,CAAgB,oBAAY,CAAC,CAAC;AAChD,QAAA,cAAc,GAAG,kBAAkB,CAAuB,SAAS,CAAC,CAAC;AAElF,MAAM,oBAAoB,GAA0B;IAClD,IAAI,EAAE,GAAG;CACV,CAAC;AACW,QAAA,kBAAkB,GAAG,aAAa,CAAwB,oBAAoB,CAAC,CAAC;AAE7F,MAAM,eAAe,GAAyB;IAC5C,QAAQ,EAAE,MAAM;IAChB,SAAS,EAAE,KAAK;IAChB,aAAa,EAAE,KAAK;CACrB,CAAC;AACW,QAAA,iBAAiB,GAAG,aAAa,CAAuB,eAAe,CAAC,CAAC;AACzE,QAAA,mBAAmB,GAAG,kBAAkB,CACnD,yBAAiB,CAClB,CAAC;AAEF,MAAM,eAAe,GAAyB;IAC5C,QAAQ,EAAE,MAAM;IAChB,UAAU,EAAE,EAAE;IACd,SAAS,EAAE,KAAK;CACjB,CAAC;AACW,QAAA,iBAAiB,GAAG,aAAa,CAAuB,eAAe,CAAC,CAAC;AACzE,QAAA,mBAAmB,GAAG,kBAAkB,CAAkC,yBAAiB,CAAC,CAAC;AAE1G,MAAM,kBAAkB,GAA4B;IAClD,QAAQ,EAAE,MAAM;IAChB,IAAI,wBAAc;IAClB,IAAI,EAAE,iBAAK,CAAC,iBAAK,CAAC,CAAC,CAAC;CACrB,CAAC;AACW,QAAA,iBAAiB,GAAG,aAAa,CAAqB,kBAAkB,CAAC,CAAC;AAC1E,QAAA,sBAAsB,GAAG,kBAAkB,CAA2B,yBAAiB,CAAC,CAAC;AAEtG,MAAM,YAAY,GAAsB;IACtC,MAAM,EAAE,KAAK;CACd,CAAC;AACK,MAAM,cAAc,GAAyC,CAAC,OAAO,GAAG,EAAE,EAAE,EAAE,CACnF,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AAD9B,QAAA,cAAc,kBACgB;AACpC,MAAM,gBAAgB,GAG2B,CAAC,OAAO,EAAE,QAAS,EAAE,EAAE,CAC7E,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,IAAA,sBAAc,GAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,sBAAc,EAAC,OAAO,CAAC,EAAE,IAAA,uBAAgB,EAAC,QAAQ,CAAC,CAAC,CAAC;AAJzG,QAAA,gBAAgB,oBAIyF;AAEtH,MAAM,gBAAgB,GAA0B,oBAAY,CAAC;AAChD,QAAA,kBAAkB,GAAG,aAAa,CAAwB,gBAAgB,CAAC,CAAC;AAC5E,QAAA,oBAAoB,GAAG,kBAAkB,CAAuC,0BAAkB,CAAC,CAAC;AAEpG,QAAA,iBAAiB,GAA2B;IACvD,QAAQ,EAAE,MAAM;IAChB,IAAI,wBAAc;IAClB,IAAI,EAAE,iBAAK,CAAC,iBAAK,CAAC,CAAC,CAAC;CACrB,CAAC;AACW,QAAA,mBAAmB,GAAG,aAAa,CAAyB,yBAAiB,CAAC,CAAC"}