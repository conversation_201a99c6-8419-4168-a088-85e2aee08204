{"version": 3, "file": "FsPromises.js", "sourceRoot": "", "sources": ["../../src/node/FsPromises.ts"], "names": [], "mappings": ";;;AAAA,iCAAqE;AACrE,4CAAyC;AAKzC,MAAa,UAAU;IAGrB,YACqB,EAAiB,EACpB,UAAwD;QADrD,OAAE,GAAF,EAAE,CAAe;QACpB,eAAU,GAAV,UAAU,CAA8C;QAJ1D,cAAS,GAAG,qBAAS,CAAC;QAOtB,OAAE,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAC9B,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,UAAK,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,UAAK,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,aAAQ,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC1C,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,SAAI,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAClC,UAAK,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,UAAK,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,aAAQ,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC1C,aAAQ,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC1C,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,UAAK,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,OAAE,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAC9B,SAAI,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAClC,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,aAAQ,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC1C,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAEtC,aAAQ,GAAG,CACzB,EAAoB,EACpB,OAAwC,EAChB,EAAE;YAC1B,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE,EAAoB,EAAE,OAAO,CAAC,CAAC;QAChH,CAAC,CAAC;QAEc,eAAU,GAAG,CAC3B,IAAsB,EACtB,IAAgB,EAChB,OAA0C,EAC3B,EAAE;YACjB,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC,CACrC,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAE,IAAsB,EACnE,IAAI,EACJ,OAAO,CACR,CAAC;QACJ,CAAC,CAAC;QAEc,SAAI,GAAG,CAAC,IAAmB,EAAE,QAAqB,GAAG,EAAE,IAAiB,EAAE,EAAE;YAC1F,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/F,CAAC,CAAC;QAEc,cAAS,GAAG,CAC1B,EAAoB,EACpB,IAAwB,EACxB,OAAgC,EACjB,EAAE;YACjB,MAAM,WAAW,GAAG,IAAA,uBAAgB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAA,qBAAc,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1F,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC7B,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE,EAAoB,EAAE,IAAI,EAAE,OAAO,CAAC,CAC9G,CAAC;QACJ,CAAC,CAAC;QAEc,UAAK,GAAG,GAAG,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC,CAAC;IAhEC,CAAC;CAiEL;AAvED,gCAuEC"}