import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest, timer } from 'rxjs';
import { map, takeUntil, filter } from 'rxjs/operators';
import { Subject } from 'rxjs';

import { WebSocketService } from './websocket.service';
import { MqttService } from './mqtt.service';
import { ConfigService } from './config.service';
import { ConnectionStatus } from '../../interfaces/websocket.interface';

export interface OverallConnectionStatus {
  websocket: ConnectionStatus;
  mqtt: ConnectionStatus;
  hasAnyConnection: boolean;
  preferredConnection: 'websocket' | 'mqtt' | 'none';
  lastSuccessfulConnection?: Date;
}

@Injectable({
  providedIn: 'root'
})
export class ConnectionManagerService implements OnDestroy {
  private destroy$ = new Subject<void>();
  private overallStatus$ = new BehaviorSubject<OverallConnectionStatus>({
    websocket: { isConnected: false, reconnectAttempts: 0 },
    mqtt: { isConnected: false, reconnectAttempts: 0 },
    hasAnyConnection: false,
    preferredConnection: 'none'
  });

  private autoReconnectEnabled = true;
  private reconnectTimer$ = timer(0, 30000); // Check every 30 seconds

  constructor(
    private webSocketService: WebSocketService,
    private mqttService: MqttService,
    private configService: ConfigService
  ) {
    this.initializeConnections();
    this.monitorConnections();
    this.setupAutoReconnect();
  }

  private initializeConnections(): void {
    // Initialize WebSocket if enabled
    if (this.configService.isWebSocketEnabled) {
      this.webSocketService.connect();
    }

    // Initialize MQTT if enabled
    if (this.configService.isMQTTEnabled) {
      this.mqttService.connect({
        brokerUrl: this.configService.mqttBrokerUrl,
        username: this.configService.mqttUsername,
        password: this.configService.mqttPassword,
        keepAlive: this.configService.mqttKeepAlive,
        reconnectPeriod: this.configService.mqttReconnectPeriod,
        topics: Object.values(this.configService.mqttTopics)
      });
    }
  }

  private monitorConnections(): void {
    // Combine connection statuses
    combineLatest([
      this.webSocketService.connectionStatus,
      this.mqttService.connectionStatus
    ]).pipe(
      takeUntil(this.destroy$),
      map(([wsStatus, mqttStatus]) => {
        const hasAnyConnection = wsStatus.isConnected || mqttStatus.isConnected;
        let preferredConnection: 'websocket' | 'mqtt' | 'none' = 'none';
        
        if (wsStatus.isConnected && mqttStatus.isConnected) {
          preferredConnection = 'websocket'; // Prefer WebSocket if both are connected
        } else if (wsStatus.isConnected) {
          preferredConnection = 'websocket';
        } else if (mqttStatus.isConnected) {
          preferredConnection = 'mqtt';
        }

        const status: OverallConnectionStatus = {
          websocket: wsStatus,
          mqtt: mqttStatus,
          hasAnyConnection,
          preferredConnection,
          lastSuccessfulConnection: hasAnyConnection ? new Date() : this.overallStatus$.value.lastSuccessfulConnection
        };

        return status;
      })
    ).subscribe(status => {
      this.overallStatus$.next(status);
      
      // Log connection changes
      if (status.hasAnyConnection !== this.overallStatus$.value.hasAnyConnection) {
        if (status.hasAnyConnection) {
          console.log('Real-time connection established via', status.preferredConnection);
        } else {
          console.warn('All real-time connections lost');
        }
      }
    });
  }

  private setupAutoReconnect(): void {
    this.reconnectTimer$.pipe(
      takeUntil(this.destroy$),
      filter(() => this.autoReconnectEnabled)
    ).subscribe(() => {
      const status = this.overallStatus$.value;
      
      // Try to reconnect WebSocket if enabled but not connected
      if (this.configService.isWebSocketEnabled && !status.websocket.isConnected) {
        if (status.websocket.reconnectAttempts < this.configService.maxReconnectAttempts) {
          console.log('Attempting WebSocket reconnection...');
          this.webSocketService.connect();
        }
      }
      
      // Try to reconnect MQTT if enabled but not connected
      if (this.configService.isMQTTEnabled && !status.mqtt.isConnected) {
        if (status.mqtt.reconnectAttempts < this.configService.maxReconnectAttempts) {
          console.log('Attempting MQTT reconnection...');
          this.mqttService.connect({
            brokerUrl: this.configService.mqttBrokerUrl,
            username: this.configService.mqttUsername,
            password: this.configService.mqttPassword,
            keepAlive: this.configService.mqttKeepAlive,
            reconnectPeriod: this.configService.mqttReconnectPeriod,
            topics: Object.values(this.configService.mqttTopics)
          });
        }
      }
    });
  }

  // Public methods
  getConnectionStatus(): Observable<OverallConnectionStatus> {
    return this.overallStatus$.asObservable();
  }

  getCurrentStatus(): OverallConnectionStatus {
    return this.overallStatus$.value;
  }

  forceReconnect(): void {
    console.log('Forcing reconnection of all services...');
    
    if (this.configService.isWebSocketEnabled) {
      this.webSocketService.disconnect();
      setTimeout(() => this.webSocketService.connect(), 1000);
    }
    
    if (this.configService.isMQTTEnabled) {
      this.mqttService.disconnect();
      setTimeout(() => this.mqttService.connect({
        brokerUrl: this.configService.mqttBrokerUrl,
        username: this.configService.mqttUsername,
        password: this.configService.mqttPassword,
        keepAlive: this.configService.mqttKeepAlive,
        reconnectPeriod: this.configService.mqttReconnectPeriod,
        topics: Object.values(this.configService.mqttTopics)
      }), 1000);
    }
  }

  disconnectAll(): void {
    console.log('Disconnecting all real-time services...');
    this.autoReconnectEnabled = false;
    
    this.webSocketService.disconnect();
    this.mqttService.disconnect();
  }

  enableAutoReconnect(): void {
    this.autoReconnectEnabled = true;
  }

  disableAutoReconnect(): void {
    this.autoReconnectEnabled = false;
  }

  subscribeToSymbols(symbols: string[]): void {
    const status = this.getCurrentStatus();
    
    if (status.preferredConnection === 'websocket') {
      this.webSocketService.subscribeToSymbols(symbols);
    } else if (status.preferredConnection === 'mqtt') {
      this.mqttService.subscribeToStockUpdates(symbols);
    } else {
      console.warn('No active connection available for subscription');
    }
  }

  unsubscribeFromSymbols(symbols: string[]): void {
    const status = this.getCurrentStatus();
    
    if (status.websocket.isConnected) {
      this.webSocketService.unsubscribeFromSymbols(symbols);
    }
    
    if (status.mqtt.isConnected) {
      symbols.forEach(symbol => {
        this.mqttService.unsubscribe(`stocks/${symbol}/quote`);
        this.mqttService.unsubscribe(`stocks/${symbol}/trades`);
      });
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.disconnectAll();
  }
}
