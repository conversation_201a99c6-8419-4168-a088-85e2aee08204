export const environment = {
  production: true,
  websocket: {
    url: 'wss://your-production-domain.com',
    reconnectInterval: 5000,
    maxReconnectAttempts: 10
  },
  mqtt: {
    brokerUrl: 'wss://your-mqtt-broker.com:8084',
    username: '',
    password: '',
    keepAlive: 60,
    reconnectPeriod: 2000,
    topics: {
      stockQuotes: 'stocks/+/quote',
      stockTrades: 'stocks/+/trades',
      marketStatus: 'market/status',
      marketIndices: 'market/indices',
      news: 'market/news'
    }
  },
  api: {
    yahooFinance: {
      baseUrl: 'https://query1.finance.yahoo.com',
      corsProxy: 'https://your-cors-proxy.com/',
      rateLimit: 1000
    },
    backend: {
      baseUrl: 'https://your-backend-api.com',
      apiVersion: 'v1'
    }
  },
  features: {
    enableWebSocket: true,
    enableMQTT: true,
    enableRealTimeUpdates: true,
    enableNotifications: true,
    enableOfflineMode: true
  },
  ui: {
    refreshInterval: 15000, // 15 seconds
    chartUpdateInterval: 500, // 0.5 seconds
    maxWatchlistItems: 100,
    defaultTheme: 'dark'
  }
};
