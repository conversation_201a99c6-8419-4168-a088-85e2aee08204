export const environment = {
  production: false,
  websocket: {
    url: 'ws://localhost:3000',
    reconnectInterval: 5000,
    maxReconnectAttempts: 5
  },
  mqtt: {
    brokerUrl: 'ws://localhost:9001',
    username: '',
    password: '',
    keepAlive: 60,
    reconnectPeriod: 1000,
    topics: {
      stockQuotes: 'stocks/+/quote',
      stockTrades: 'stocks/+/trades',
      marketStatus: 'market/status',
      marketIndices: 'market/indices',
      news: 'market/news'
    }
  },
  api: {
    yahooFinance: {
      baseUrl: 'https://query1.finance.yahoo.com',
      corsProxy: '', // Add CORS proxy if needed
      rateLimit: 2000 // milliseconds between requests
    },
    backend: {
      baseUrl: 'http://localhost:8000', // Python backend URL
      apiVersion: 'v1'
    }
  },
  features: {
    enableWebSocket: true,
    enableMQTT: true,
    enableRealTimeUpdates: true,
    enableNotifications: true,
    enableOfflineMode: false
  },
  ui: {
    refreshInterval: 30000, // 30 seconds
    chartUpdateInterval: 1000, // 1 second
    maxWatchlistItems: 50,
    defaultTheme: 'dark'
  }
};
