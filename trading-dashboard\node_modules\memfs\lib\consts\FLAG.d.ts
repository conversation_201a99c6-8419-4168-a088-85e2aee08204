/**
 * Constants used in `open` system calls, see [open(2)](http://man7.org/linux/man-pages/man2/open.2.html).
 *
 * @see http://man7.org/linux/man-pages/man2/open.2.html
 * @see https://www.gnu.org/software/libc/manual/html_node/Open_002dtime-Flags.html
 */
export declare const enum FLAG {
    O_RDONLY = 0,
    O_WRONLY = 1,
    O_RDWR = 2,
    O_ACCMODE = 3,
    O_CREAT = 64,
    O_EXCL = 128,
    O_NOCTTY = 256,
    O_TRUNC = 512,
    O_APPEND = 1024,
    O_NONBLOCK = 2048,
    O_DSYNC = 4096,
    FASYNC = 8192,
    O_DIRECT = 16384,
    O_LARGEFILE = 0,
    O_DIRECTORY = 65536,
    O_NOFOLLOW = 131072,
    O_NOATIME = 262144,
    O_CLOEXEC = 524288,
    O_SYNC = 1052672,
    O_NDELAY = 2048
}
