import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest, merge } from 'rxjs';
import { map, filter, distinctUntilChanged } from 'rxjs/operators';
import { StockModel } from '../../models/stock.model';
import { Stock, MarketIndex, WatchlistItem } from '../../interfaces/stock.interface';
import { WebSocketService } from './websocket.service';
import { MqttService } from './mqtt.service';
import { YahooFinanceService } from './yahoo-finance.service';

@Injectable({
  providedIn: 'root'
})
export class DataService {
  private stocks$ = new BehaviorSubject<Map<string, StockModel>>(new Map());
  private marketIndices$ = new BehaviorSubject<MarketIndex[]>([]);
  private watchlist$ = new BehaviorSubject<WatchlistItem[]>([]);
  private isLoading$ = new BehaviorSubject<boolean>(false);
  private lastUpdate$ = new BehaviorSubject<Date>(new Date());

  constructor(
    private webSocketService: WebSocketService,
    private mqttService: MqttService,
    private yahooFinanceService: YahooFinanceService
  ) {
    this.initializeRealTimeUpdates();
    this.loadInitialData();
  }

  private initializeRealTimeUpdates(): void {
    // WebSocket stock updates
    this.webSocketService.stockUpdates.subscribe(message => {
      this.updateStockData(message.data.symbol, {
        price: message.data.price,
        change: message.data.change,
        changePercent: message.data.changePercent,
        volume: message.data.volume,
        timestamp: message.data.timestamp
      });
    });

    // MQTT stock updates
    this.mqttService.messages.pipe(
      filter(message => message.topic.startsWith('stocks/') && message.topic.endsWith('/quote'))
    ).subscribe(message => {
      const symbol = this.extractSymbolFromTopic(message.topic);
      if (symbol && message.payload) {
        this.updateStockData(symbol, message.payload);
      }
    });
  }

  private loadInitialData(): void {
    // Load market indices
    this.loadMarketIndices();
    
    // Load watchlist from localStorage
    this.loadWatchlistFromStorage();
  }

  // Stock data methods
  addStock(symbol: string): Observable<StockModel> {
    this.isLoading$.next(true);
    
    return this.yahooFinanceService.getStockQuote(symbol).pipe(
      map(stock => {
        const currentStocks = this.stocks$.value;
        currentStocks.set(symbol, stock);
        this.stocks$.next(new Map(currentStocks));
        this.isLoading$.next(false);
        this.lastUpdate$.next(new Date());
        
        // Subscribe to real-time updates
        this.subscribeToRealTimeUpdates([symbol]);
        
        return stock;
      })
    );
  }

  removeStock(symbol: string): void {
    const currentStocks = this.stocks$.value;
    if (currentStocks.has(symbol)) {
      currentStocks.delete(symbol);
      this.stocks$.next(new Map(currentStocks));
      
      // Unsubscribe from real-time updates
      this.unsubscribeFromRealTimeUpdates([symbol]);
    }
  }

  getStock(symbol: string): Observable<StockModel | undefined> {
    return this.stocks$.pipe(
      map(stocks => stocks.get(symbol)),
      distinctUntilChanged()
    );
  }

  getAllStocks(): Observable<StockModel[]> {
    return this.stocks$.pipe(
      map(stocks => Array.from(stocks.values()))
    );
  }

  refreshStock(symbol: string): Observable<StockModel> {
    return this.yahooFinanceService.getStockQuote(symbol).pipe(
      map(stock => {
        const currentStocks = this.stocks$.value;
        currentStocks.set(symbol, stock);
        this.stocks$.next(new Map(currentStocks));
        this.lastUpdate$.next(new Date());
        return stock;
      })
    );
  }

  refreshAllStocks(): void {
    const symbols = Array.from(this.stocks$.value.keys());
    if (symbols.length === 0) return;

    this.isLoading$.next(true);
    
    this.yahooFinanceService.getMultipleStockQuotes(symbols).subscribe({
      next: (stocks) => {
        const stockMap = new Map<string, StockModel>();
        stocks.forEach(stock => stockMap.set(stock.symbol, stock));
        this.stocks$.next(stockMap);
        this.isLoading$.next(false);
        this.lastUpdate$.next(new Date());
      },
      error: (error) => {
        console.error('Error refreshing stocks:', error);
        this.isLoading$.next(false);
      }
    });
  }

  // Market indices methods
  loadMarketIndices(): void {
    this.yahooFinanceService.getMarketIndices().subscribe({
      next: (indices) => {
        this.marketIndices$.next(indices);
      },
      error: (error) => {
        console.error('Error loading market indices:', error);
      }
    });
  }

  getMarketIndices(): Observable<MarketIndex[]> {
    return this.marketIndices$.asObservable();
  }

  // Watchlist methods
  addToWatchlist(symbol: string, alertPrice?: number, notes?: string): void {
    const currentWatchlist = this.watchlist$.value;
    const existingItem = currentWatchlist.find(item => item.symbol === symbol);
    
    if (!existingItem) {
      const newItem: WatchlistItem = {
        id: this.generateId(),
        symbol,
        addedDate: new Date(),
        alertPrice,
        notes
      };
      
      const updatedWatchlist = [...currentWatchlist, newItem];
      this.watchlist$.next(updatedWatchlist);
      this.saveWatchlistToStorage(updatedWatchlist);
      
      // Add stock to tracking
      this.addStock(symbol).subscribe();
    }
  }

  removeFromWatchlist(symbol: string): void {
    const currentWatchlist = this.watchlist$.value;
    const updatedWatchlist = currentWatchlist.filter(item => item.symbol !== symbol);
    this.watchlist$.next(updatedWatchlist);
    this.saveWatchlistToStorage(updatedWatchlist);
  }

  getWatchlist(): Observable<WatchlistItem[]> {
    return this.watchlist$.asObservable();
  }

  // Real-time subscription methods
  private subscribeToRealTimeUpdates(symbols: string[]): void {
    // Subscribe via WebSocket
    if (this.webSocketService.isConnected) {
      this.webSocketService.subscribeToSymbols(symbols);
    }
    
    // Subscribe via MQTT
    if (this.mqttService.isConnected) {
      this.mqttService.subscribeToStockUpdates(symbols);
    }
  }

  private unsubscribeFromRealTimeUpdates(symbols: string[]): void {
    // Unsubscribe from WebSocket
    if (this.webSocketService.isConnected) {
      this.webSocketService.unsubscribeFromSymbols(symbols);
    }
    
    // Unsubscribe from MQTT
    symbols.forEach(symbol => {
      this.mqttService.unsubscribe(`stocks/${symbol}/quote`);
      this.mqttService.unsubscribe(`stocks/${symbol}/trades`);
    });
  }

  // Utility methods
  private updateStockData(symbol: string, updateData: Partial<Stock>): void {
    const currentStocks = this.stocks$.value;
    const existingStock = currentStocks.get(symbol);
    
    if (existingStock) {
      const updatedStock = new StockModel({
        ...existingStock,
        ...updateData,
        timestamp: new Date()
      });
      
      currentStocks.set(symbol, updatedStock);
      this.stocks$.next(new Map(currentStocks));
      this.lastUpdate$.next(new Date());
    }
  }

  private extractSymbolFromTopic(topic: string): string | null {
    const match = topic.match(/^stocks\/([^\/]+)\//);
    return match ? match[1] : null;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private saveWatchlistToStorage(watchlist: WatchlistItem[]): void {
    localStorage.setItem('trading_watchlist', JSON.stringify(watchlist));
  }

  private loadWatchlistFromStorage(): void {
    try {
      const stored = localStorage.getItem('trading_watchlist');
      if (stored) {
        const watchlist = JSON.parse(stored);
        this.watchlist$.next(watchlist);
        
        // Load stocks for watchlist items
        const symbols = watchlist.map((item: WatchlistItem) => item.symbol);
        if (symbols.length > 0) {
          this.yahooFinanceService.getMultipleStockQuotes(symbols).subscribe({
            next: (stocks) => {
              const stockMap = new Map<string, StockModel>();
              stocks.forEach(stock => stockMap.set(stock.symbol, stock));
              this.stocks$.next(stockMap);
              
              // Subscribe to real-time updates
              this.subscribeToRealTimeUpdates(symbols);
            },
            error: (error) => {
              console.error('Error loading watchlist stocks:', error);
            }
          });
        }
      }
    } catch (error) {
      console.error('Error loading watchlist from storage:', error);
    }
  }

  // Observable getters
  get isLoading(): Observable<boolean> {
    return this.isLoading$.asObservable();
  }

  get lastUpdate(): Observable<Date> {
    return this.lastUpdate$.asObservable();
  }

  // Connection status
  get connectionStatus(): Observable<any> {
    return combineLatest([
      this.webSocketService.connectionStatus,
      this.mqttService.connectionStatus
    ]).pipe(
      map(([wsStatus, mqttStatus]) => ({
        websocket: wsStatus,
        mqtt: mqttStatus,
        hasConnection: wsStatus.isConnected || mqttStatus.isConnected
      }))
    );
  }
}
