export type PathTypeFunction = (path: string) => Promise<boolean>;

/**
Check whether the passed `path` is a file.

@param path - The path to check.
@returns Whether the `path` is a file.
*/
export const isFile: PathTypeFunction;

/**
Check whether the passed `path` is a directory.

@param path - The path to check.
@returns Whether the `path` is a directory.
*/
export const isDirectory: PathTypeFunction;

/**
Check whether the passed `path` is a symlink.

@param path - The path to check.
@returns Whether the `path` is a symlink.
*/
export const isSymlink: PathTypeFunction;

export type PathTypeSyncFunction = (path: string) => boolean;

/**
Synchronously check whether the passed `path` is a file.

@param path - The path to check.
@returns Whether the `path` is a file.
*/
export const isFileSync: PathTypeSyncFunction;

/**
Synchronously check whether the passed `path` is a directory.

@param path - The path to check.
@returns Whether the `path` is a directory.
*/
export const isDirectorySync: PathTypeSyncFunction;

/**
Synchronously check whether the passed `path` is a symlink.

@param path - The path to check.
@returns Whether the `path` is a directory.
*/
export const isSymlinkSync: PathTypeSyncFunction;
