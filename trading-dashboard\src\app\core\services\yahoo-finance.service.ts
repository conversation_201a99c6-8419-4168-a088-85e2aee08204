import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, retry } from 'rxjs/operators';
import { Stock, StockHistoricalData, MarketIndex } from '../../interfaces/stock.interface';
import { StockModel } from '../../models/stock.model';

@Injectable({
  providedIn: 'root'
})
export class YahooFinanceService {
  private readonly baseUrl = 'https://query1.finance.yahoo.com/v8/finance/chart';
  private readonly quotesUrl = 'https://query1.finance.yahoo.com/v7/finance/quote';
  private readonly searchUrl = 'https://query1.finance.yahoo.com/v1/finance/search';
  
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly cacheTimeout = 30000; // 30 seconds

  constructor(private http: HttpClient) {}

  getStockQuote(symbol: string): Observable<StockModel> {
    const cacheKey = `quote_${symbol}`;
    const cached = this.getCachedData(cacheKey);
    
    if (cached) {
      return new Observable(observer => {
        observer.next(StockModel.fromYahooFinanceData(cached));
        observer.complete();
      });
    }

    const params = new HttpParams().set('symbols', symbol);
    
    return this.http.get<any>(`${this.quotesUrl}`, { params }).pipe(
      map(response => {
        if (response.quoteResponse?.result?.length > 0) {
          const stockData = response.quoteResponse.result[0];
          this.setCachedData(cacheKey, stockData);
          return StockModel.fromYahooFinanceData(stockData);
        }
        throw new Error(`No data found for symbol: ${symbol}`);
      }),
      retry(2),
      catchError(error => {
        console.error('Error fetching stock quote:', error);
        return throwError(() => new Error(`Failed to fetch quote for ${symbol}`));
      })
    );
  }

  getMultipleStockQuotes(symbols: string[]): Observable<StockModel[]> {
    const symbolsParam = symbols.join(',');
    const params = new HttpParams().set('symbols', symbolsParam);
    
    return this.http.get<any>(`${this.quotesUrl}`, { params }).pipe(
      map(response => {
        if (response.quoteResponse?.result) {
          return response.quoteResponse.result.map((stockData: any) => 
            StockModel.fromYahooFinanceData(stockData)
          );
        }
        return [];
      }),
      retry(2),
      catchError(error => {
        console.error('Error fetching multiple stock quotes:', error);
        return throwError(() => new Error('Failed to fetch stock quotes'));
      })
    );
  }

  getHistoricalData(
    symbol: string, 
    period: '1d' | '5d' | '1mo' | '3mo' | '6mo' | '1y' | '2y' | '5y' | '10y' | 'ytd' | 'max' = '1y',
    interval: '1m' | '2m' | '5m' | '15m' | '30m' | '60m' | '90m' | '1h' | '1d' | '5d' | '1wk' | '1mo' | '3mo' = '1d'
  ): Observable<StockHistoricalData[]> {
    const params = new HttpParams()
      .set('period1', this.getPeriodTimestamp(period).toString())
      .set('period2', Math.floor(Date.now() / 1000).toString())
      .set('interval', interval)
      .set('includePrePost', 'true')
      .set('events', 'div,splits');

    return this.http.get<any>(`${this.baseUrl}/${symbol}`, { params }).pipe(
      map(response => {
        const result = response.chart?.result?.[0];
        if (!result) {
          throw new Error(`No historical data found for symbol: ${symbol}`);
        }

        const timestamps = result.timestamp || [];
        const quotes = result.indicators?.quote?.[0] || {};
        const adjClose = result.indicators?.adjclose?.[0]?.adjclose || [];

        return timestamps.map((timestamp: number, index: number) => ({
          symbol,
          date: new Date(timestamp * 1000),
          open: quotes.open?.[index] || 0,
          high: quotes.high?.[index] || 0,
          low: quotes.low?.[index] || 0,
          close: quotes.close?.[index] || 0,
          volume: quotes.volume?.[index] || 0,
          adjustedClose: adjClose[index] || quotes.close?.[index] || 0
        }));
      }),
      retry(2),
      catchError(error => {
        console.error('Error fetching historical data:', error);
        return throwError(() => new Error(`Failed to fetch historical data for ${symbol}`));
      })
    );
  }

  searchStocks(query: string): Observable<any[]> {
    const params = new HttpParams().set('q', query);
    
    return this.http.get<any>(`${this.searchUrl}`, { params }).pipe(
      map(response => {
        return response.quotes || [];
      }),
      retry(2),
      catchError(error => {
        console.error('Error searching stocks:', error);
        return throwError(() => new Error('Failed to search stocks'));
      })
    );
  }

  getMarketIndices(): Observable<MarketIndex[]> {
    const indices = ['^GSPC', '^DJI', '^IXIC', '^RUT']; // S&P 500, Dow Jones, NASDAQ, Russell 2000
    
    return this.getMultipleStockQuotes(indices).pipe(
      map(stocks => stocks.map(stock => ({
        symbol: stock.symbol,
        name: this.getIndexName(stock.symbol),
        value: stock.price,
        change: stock.change,
        changePercent: stock.changePercent,
        timestamp: stock.timestamp
      })))
    );
  }

  // Utility methods
  private getPeriodTimestamp(period: string): number {
    const now = Date.now();
    const day = 24 * 60 * 60 * 1000;
    
    switch (period) {
      case '1d': return Math.floor((now - day) / 1000);
      case '5d': return Math.floor((now - 5 * day) / 1000);
      case '1mo': return Math.floor((now - 30 * day) / 1000);
      case '3mo': return Math.floor((now - 90 * day) / 1000);
      case '6mo': return Math.floor((now - 180 * day) / 1000);
      case '1y': return Math.floor((now - 365 * day) / 1000);
      case '2y': return Math.floor((now - 730 * day) / 1000);
      case '5y': return Math.floor((now - 1825 * day) / 1000);
      case '10y': return Math.floor((now - 3650 * day) / 1000);
      case 'ytd': {
        const yearStart = new Date(new Date().getFullYear(), 0, 1);
        return Math.floor(yearStart.getTime() / 1000);
      }
      case 'max': return 0;
      default: return Math.floor((now - 365 * day) / 1000);
    }
  }

  private getIndexName(symbol: string): string {
    const indexNames: { [key: string]: string } = {
      '^GSPC': 'S&P 500',
      '^DJI': 'Dow Jones Industrial Average',
      '^IXIC': 'NASDAQ Composite',
      '^RUT': 'Russell 2000'
    };
    return indexNames[symbol] || symbol;
  }

  private getCachedData(key: string): any {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  clearCache(): void {
    this.cache.clear();
  }
}
