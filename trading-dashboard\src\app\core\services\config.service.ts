import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  private config = environment;

  constructor() {}

  // WebSocket configuration
  get websocketUrl(): string {
    return this.config.websocket.url;
  }

  get websocketReconnectInterval(): number {
    return this.config.websocket.reconnectInterval;
  }

  get maxReconnectAttempts(): number {
    return this.config.websocket.maxReconnectAttempts;
  }

  // MQTT configuration
  get mqttBrokerUrl(): string {
    return this.config.mqtt.brokerUrl;
  }

  get mqttUsername(): string {
    return this.config.mqtt.username;
  }

  get mqttPassword(): string {
    return this.config.mqtt.password;
  }

  get mqttKeepAlive(): number {
    return this.config.mqtt.keepAlive;
  }

  get mqttReconnectPeriod(): number {
    return this.config.mqtt.reconnectPeriod;
  }

  get mqttTopics(): any {
    return this.config.mqtt.topics;
  }

  // API configuration
  get yahooFinanceBaseUrl(): string {
    return this.config.api.yahooFinance.baseUrl;
  }

  get yahooFinanceCorsProxy(): string {
    return this.config.api.yahooFinance.corsProxy;
  }

  get yahooFinanceRateLimit(): number {
    return this.config.api.yahooFinance.rateLimit;
  }

  get backendBaseUrl(): string {
    return this.config.api.backend.baseUrl;
  }

  get backendApiVersion(): string {
    return this.config.api.backend.apiVersion;
  }

  // Feature flags
  get isWebSocketEnabled(): boolean {
    return this.config.features.enableWebSocket;
  }

  get isMQTTEnabled(): boolean {
    return this.config.features.enableMQTT;
  }

  get isRealTimeUpdatesEnabled(): boolean {
    return this.config.features.enableRealTimeUpdates;
  }

  get isNotificationsEnabled(): boolean {
    return this.config.features.enableNotifications;
  }

  get isOfflineModeEnabled(): boolean {
    return this.config.features.enableOfflineMode;
  }

  // UI configuration
  get refreshInterval(): number {
    return this.config.ui.refreshInterval;
  }

  get chartUpdateInterval(): number {
    return this.config.ui.chartUpdateInterval;
  }

  get maxWatchlistItems(): number {
    return this.config.ui.maxWatchlistItems;
  }

  get defaultTheme(): string {
    return this.config.ui.defaultTheme;
  }

  // Production flag
  get isProduction(): boolean {
    return this.config.production;
  }

  // Update configuration at runtime
  updateConfig(updates: any): void {
    this.config = { ...this.config, ...updates };
  }

  // Get full configuration
  getConfig(): any {
    return { ...this.config };
  }
}
