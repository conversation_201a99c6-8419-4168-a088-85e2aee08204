{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/internal/errors.ts"], "names": [], "mappings": ";AAAA,wEAAwE;AACxE,yEAAyE;AACzE,wEAAwE;AACxE,yEAAyE;AACzE,2CAA2C;;;AA+EzC,0BAAO;AAEP,cAAC;AA/EH,iCAAiC;AACjC,6BAA6B;AAE7B,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAE,MAAc,CAAC,MAAM,CAAC,CAAC;AACjF,MAAM,QAAQ,GAAG,EAAE,CAAC,CAAC,aAAa;AAElC,SAAS,aAAa,CAAC,IAAI;IACzB,OAAO,MAAM,SAAU,SAAQ,IAAI;QACjC,YAAY,GAAG,EAAE,GAAG,IAAI;YACtB,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;YAChB,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;YAClB,IAAI,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC/C,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,GAAG,OAAO,UAAU,KAAK,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AAElE,MAAM,cAAe,SAAQ,CAAC,CAAC,KAAK;IAQlC,YAAY,OAAO;QACjB,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACpD,MAAM,IAAI,OAAO,CAAC,SAAS,CAAC,sBAAsB,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC3E,CAAC;QACD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,KAAK,CACH,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG;gBAC9C,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACxE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,IAAI,GAAG,gCAAgC,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,aAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAC5D,CAAC;CACF;AA8BC,wCAAc;AA5BhB,SAAS,OAAO,CAAC,GAAG,EAAE,IAAI;IACxB,MAAM,CAAC,WAAW,CAAC,OAAO,GAAG,EAAE,QAAQ,CAAC,CAAC;IACzC,iCAAiC;IACjC,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC1B,MAAM,CAAC,GAAG,EAAE,0CAA0C,GAAG,GAAG,CAAC,CAAC;IAC9D,IAAI,GAAG,CAAC;IACR,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE,CAAC;QAC9B,GAAG,GAAG,GAAG,CAAC;IACZ,CAAC;SAAM,CAAC;QACN,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;QAClB,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,GAAG,CAAC;QACxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IACD,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACvC,CAAC;AAED,6EAA6E;AAC7E,+BAA+B;AAC/B,SAAS,CAAC,CAAC,GAAG,EAAE,GAAG;IACjB,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAChE,CAAC;AAEY,QAAA,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AACvC,QAAA,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AAQtD,2EAA2E;AAC3E,8EAA8E;AAC9E,qDAAqD;AACrD,YAAY;AACZ,gDAAgD;AAChD,mDAAmD;AACnD,EAAE;AACF,4EAA4E;AAC5E,2EAA2E;AAC3E,EAAE;AACF,sEAAsE;AACtE,EAAE;AACF,uDAAuD;AACvD,CAAC,CAAC,sBAAsB,EAAE,qBAAqB,CAAC,CAAC;AACjD,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AACzB,CAAC,CAAC,0BAA0B,EAAE,iBAAiB,CAAC,CAAC;AACjD,CAAC,CAAC,+BAA+B,EAAE,oCAAoC,CAAC,CAAC;AACzE,CAAC,CAAC,6BAA6B,EAAE,mDAAmD,CAAC,CAAC;AACtF,CAAC,CAAC,eAAe,EAAE,+BAA+B,CAAC,CAAC;AACpD,CAAC,CAAC,4BAA4B,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,kCAAkC,GAAG,MAAM,OAAO,GAAG,CAAC,CAAC;AACzG,CAAC,CAAC,2BAA2B,EAAE,uCAAuC,CAAC,CAAC;AACxE,CAAC,CAAC,4BAA4B,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,GAAG,6BAA6B,CAAC,CAAC;AACjF,CAAC,CAAC,mCAAmC,EAAE,GAAG,CAAC,EAAE,CAAC,+CAA+C,GAAG,EAAE,CAAC,CAAC;AACpG,CAAC,CAAC,uBAAuB,EAAE,yDAAyD,CAAC,CAAC;AACtF,CAAC,CAAC,8BAA8B,EAAE,yBAAyB,CAAC,CAAC;AAC7D,CAAC,CAAC,0BAA0B,EAAE,kDAAkD,CAAC,CAAC;AAClF,CAAC,CAAC,wBAAwB,EAAE,oBAAoB,CAAC,CAAC;AAClD,CAAC,CAAC,sBAAsB,EAAE,cAAc,CAAC,CAAC;AAC1C,CAAC,CAAC,0BAA0B,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE;IAClD,MAAM,CAAC,WAAW,CAAC,OAAO,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC5C,OAAO,cAAc,IAAI,aAAa,MAAM,uBAAuB,GAAG,GAAG,CAAC;AAC5E,CAAC,CAAC,CAAC;AACH,CAAC,CAAC,yBAAyB,EAAE,sCAAsC,CAAC,CAAC;AACrE,CAAC,CAAC,sBAAsB,EAAE,6BAA6B,CAAC,CAAC;AACzD,CAAC,CAAC,kBAAkB,EAAE,yBAAyB,CAAC,CAAC;AACjD,CAAC,CAAC,wBAAwB,EAAE,kDAAkD,CAAC,CAAC;AAChF,CAAC,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,CAAC;AAC3D,CAAC,CAAC,2BAA2B,EAAE,kDAAkD,CAAC,CAAC;AACnF,CAAC,CAAC,2BAA2B,EAAE,kBAAkB,CAAC,CAAC;AACnD,CAAC,CAAC,yBAAyB,EAAE,iCAAiC,CAAC,CAAC;AAChE,CAAC,CAAC,wBAAwB,EAAE,wBAAwB,CAAC,CAAC;AACtD,CAAC,CAAC,uBAAuB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACzC,OAAO,cAAc,MAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,GAAG,CAAC;AACxE,CAAC,CAAC,CAAC;AACH,CAAC,CAAC,gCAAgC,EAAE,KAAK,CAAC,EAAE,CAAC,cAAc,MAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAC9G,CAAC,CAAC,8BAA8B,EAAE,6DAA6D,CAAC,CAAC;AACjG,CAAC,CAAC,6BAA6B,EAAE,0EAA0E,CAAC,CAAC;AAC7G,CAAC,CAAC,kBAAkB,EAAE,oCAAoC,CAAC,CAAC;AAC5D,CAAC,CAAC,mBAAmB,EAAE,iCAAiC,CAAC,CAAC;AAC1D,CAAC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;AACxC,CAAC,CAAC,wBAAwB,EAAE,QAAQ,CAAC,EAAE,CAAC,mBAAmB,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;AACxF,CAAC,CAAC,wBAAwB,EAAE,gBAAgB,CAAC,CAAC;AAC9C,CAAC,CAAC,sBAAsB,EAAE,qCAAqC,CAAC,CAAC;AACjE,CAAC,CAAC,kBAAkB,EAAE,0CAA0C,CAAC,CAAC;AAClE,CAAC,CAAC,mBAAmB,EAAE,2CAA2C,CAAC,CAAC;AACpE,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;AACnC,CAAC,CAAC,uBAAuB,EAAE,gCAAgC,CAAC,CAAC;AAC7D,CAAC,CAAC,wBAAwB,EAAE,gCAAgC,CAAC,CAAC;AAC9D,CAAC,CAAC,gCAAgC,EAAE,yCAAyC,CAAC,CAAC;AAC/E,CAAC,CAAC,eAAe,EAAE,qDAAqD,CAAC,CAAC;AAC1E,CAAC,CAAC,yBAAyB,EAAE,2BAA2B,CAAC,CAAC;AAC1D,CAAC,CAAC,wBAAwB,EAAE,oCAAoC,CAAC,CAAC;AAClE,CAAC,CAAC,0BAA0B,EAAE,yBAAyB,CAAC,CAAC;AACzD,CAAC,CAAC,qBAAqB,EAAE,gCAAgC,CAAC,CAAC;AAC3D,CAAC,CAAC,qBAAqB,EAAE,wDAAwD,CAAC,CAAC;AACnF,CAAC,CAAC,wBAAwB,EAAE,qBAAqB,CAAC,CAAC;AACnD,CAAC,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;AAC3C,CAAC,CAAC,8BAA8B,EAAE,aAAa,CAAC,CAAC;AACjD,CAAC,CAAC,kBAAkB,EAAE,iCAAiC,CAAC,CAAC;AACzD,CAAC,CAAC,kBAAkB,EAAE,iCAAiC,CAAC,CAAC;AACzD,CAAC,CAAC,iBAAiB,EAAE,kDAAkD,CAAC,CAAC;AACzE,CAAC,CAAC,8BAA8B,EAAE,uDAAuD,CAAC,CAAC;AAC3F,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC,EAAE,CAAC,qBAAqB,IAAI,oBAAoB,CAAC,CAAC;AAClF,CAAC,CAAC,2BAA2B,EAAE,uBAAuB,CAAC,CAAC;AACxD,CAAC,CAAC,8BAA8B,EAAE,uBAAuB,CAAC,CAAC;AAC3D,CAAC,CAAC,8BAA8B,EAAE,0DAA0D,CAAC,CAAC;AAC9F,CAAC,CAAC,wBAAwB,EAAE,2CAA2C,CAAC,CAAC;AACzE,CAAC,CAAC,oCAAoC,EAAE,gDAAgD,CAAC,CAAC;AAC1F,CAAC,CAAC,6BAA6B,EAAE,uDAAuD,CAAC,CAAC;AAC1F,CAAC,CAAC,sBAAsB,EAAE,sBAAsB,CAAC,CAAC;AAClD,CAAC,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,CAAC;AAC9C,CAAC,CAAC,wBAAwB,EAAE,yBAAyB,CAAC,CAAC;AACvD,CAAC,CAAC,yBAAyB,EAAE,0BAA0B,CAAC,CAAC;AACzD,CAAC,CAAC,qBAAqB,EAAE,+BAA+B,GAAG,8CAA8C,CAAC,CAAC;AAE3G,SAAS,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM;IAC5C,MAAM,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;IAEjC,yCAAyC;IACzC,IAAI,UAAU,CAAC;IACf,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9B,UAAU,GAAG,aAAa,CAAC;QAC3B,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;SAAM,CAAC;QACN,UAAU,GAAG,SAAS,CAAC;IACzB,CAAC;IAED,IAAI,GAAG,CAAC;IACR,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,GAAG,GAAG,OAAO,KAAK,cAAc,UAAU,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;IAC1E,CAAC;SAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACtC,qCAAqC;QACrC,GAAG,GAAG,OAAO,IAAI,IAAI,UAAU,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;IAC/D,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC;QAC1D,GAAG,GAAG,QAAQ,IAAI,KAAK,IAAI,IAAI,UAAU,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;IACzE,CAAC;IAED,sCAAsC;IACtC,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;QAC1B,GAAG,IAAI,mBAAmB,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IACvE,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,WAAW,CAAC,GAAG,IAAI;IAC1B,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,wCAAwC,CAAC,CAAC;IAClE,IAAI,GAAG,GAAG,MAAM,CAAC;IACjB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;IACxB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/B,QAAQ,GAAG,EAAE,CAAC;QACZ,KAAK,CAAC;YACJ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC;YAC7B,MAAM;QACR,KAAK,CAAC;YACJ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC;YAC7C,MAAM;QACR;YACE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,GAAG,IAAI,SAAS,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,YAAY,CAAC;YAC1C,MAAM;IACV,CAAC;IACD,OAAO,GAAG,GAAG,oBAAoB,CAAC;AACpC,CAAC;AAED,SAAS,KAAK,CAAC,QAAQ,EAAE,KAAK;IAC5B,MAAM,CAAC,QAAQ,EAAE,sBAAsB,CAAC,CAAC;IACzC,MAAM,CAAC,OAAO,KAAK,KAAK,QAAQ,EAAE,mBAAmB,CAAC,CAAC;IACvD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC5B,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC5B,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,mDAAmD,CAAC,CAAC;QACrE,2BAA2B;QAC3B,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;YACZ,OAAO,UAAU,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAC7F,CAAC;aAAM,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,UAAU,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QACtC,CAAC;IACH,CAAC;SAAM,CAAC;QACN,OAAO,MAAM,KAAK,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IAC3C,CAAC;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAI,EAAE,SAAS;IACxC,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,wCAAwC,CAAC;IAClD,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,IAAI,+BAA+B,CAAC;IACjD,CAAC;AACH,CAAC"}