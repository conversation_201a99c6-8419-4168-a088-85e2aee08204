import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import * as mqtt from 'mqtt';
import { MQTTConfig, MQTTMessage, ConnectionStatus } from '../../interfaces/websocket.interface';

@Injectable({
  providedIn: 'root'
})
export class MqttService {
  private client: mqtt.MqttClient | null = null;
  private connectionStatus$ = new BehaviorSubject<ConnectionStatus>({
    isConnected: false,
    reconnectAttempts: 0
  });
  
  private messages$ = new Subject<MQTTMessage>();
  private subscribedTopics: Set<string> = new Set();

  private defaultConfig: MQTTConfig = {
    brokerUrl: 'ws://localhost:9001', // WebSocket port for MQTT broker
    clientId: `trading_dashboard_${Math.random().toString(16).substr(2, 8)}`,
    keepAlive: 60,
    reconnectPeriod: 1000,
    topics: []
  };

  constructor() {}

  connect(config?: Partial<MQTTConfig>): void {
    const mqttConfig = { ...this.defaultConfig, ...config };

    try {
      const options: mqtt.IClientOptions = {
        clientId: mqttConfig.clientId,
        keepalive: mqttConfig.keepAlive,
        reconnectPeriod: mqttConfig.reconnectPeriod,
        clean: true,
        username: mqttConfig.username,
        password: mqttConfig.password
      };

      this.client = mqtt.connect(mqttConfig.brokerUrl, options);
      this.setupEventListeners();

      // Subscribe to default topics if provided
      if (mqttConfig.topics.length > 0) {
        this.client.on('connect', () => {
          mqttConfig.topics.forEach(topic => this.subscribe(topic));
        });
      }

    } catch (error) {
      console.error('Failed to connect to MQTT broker:', error);
      this.updateConnectionStatus({
        isConnected: false,
        error: 'Failed to connect to MQTT broker'
      });
    }
  }

  private setupEventListeners(): void {
    if (!this.client) return;

    this.client.on('connect', () => {
      console.log('MQTT connected');
      this.updateConnectionStatus({
        isConnected: true,
        lastConnected: new Date(),
        reconnectAttempts: 0
      });
    });

    this.client.on('disconnect', () => {
      console.log('MQTT disconnected');
      this.updateConnectionStatus({
        isConnected: false
      });
    });

    this.client.on('error', (error) => {
      console.error('MQTT error:', error);
      const currentStatus = this.connectionStatus$.value;
      this.updateConnectionStatus({
        isConnected: false,
        reconnectAttempts: currentStatus.reconnectAttempts + 1,
        error: error.message
      });
    });

    this.client.on('message', (topic: string, payload: Buffer, packet: mqtt.IPublishPacket) => {
      try {
        const message: MQTTMessage = {
          topic,
          payload: this.parsePayload(payload),
          qos: packet.qos as (0 | 1 | 2),
          retain: packet.retain || false,
          timestamp: new Date()
        };
        this.messages$.next(message);
      } catch (error) {
        console.error('Error parsing MQTT message:', error);
      }
    });

    this.client.on('reconnect', () => {
      console.log('MQTT reconnecting...');
      const currentStatus = this.connectionStatus$.value;
      this.updateConnectionStatus({
        reconnectAttempts: currentStatus.reconnectAttempts + 1
      });
    });
  }

  subscribe(topic: string, qos: 0 | 1 | 2 = 0): void {
    if (!this.client || !this.client.connected) {
      console.warn('MQTT client not connected. Cannot subscribe to topic:', topic);
      return;
    }

    this.client.subscribe(topic, { qos }, (error) => {
      if (error) {
        console.error('Failed to subscribe to topic:', topic, error);
      } else {
        console.log('Subscribed to topic:', topic);
        this.subscribedTopics.add(topic);
      }
    });
  }

  unsubscribe(topic: string): void {
    if (!this.client || !this.client.connected) {
      console.warn('MQTT client not connected. Cannot unsubscribe from topic:', topic);
      return;
    }

    this.client.unsubscribe(topic, (error) => {
      if (error) {
        console.error('Failed to unsubscribe from topic:', topic, error);
      } else {
        console.log('Unsubscribed from topic:', topic);
        this.subscribedTopics.delete(topic);
      }
    });
  }

  publish(topic: string, message: any, qos: 0 | 1 | 2 = 0, retain: boolean = false): void {
    if (!this.client || !this.client.connected) {
      console.warn('MQTT client not connected. Cannot publish to topic:', topic);
      return;
    }

    const payload = typeof message === 'string' ? message : JSON.stringify(message);
    
    this.client.publish(topic, payload, { qos, retain }, (error) => {
      if (error) {
        console.error('Failed to publish to topic:', topic, error);
      } else {
        console.log('Published to topic:', topic);
      }
    });
  }

  disconnect(): void {
    if (this.client && this.client.connected) {
      this.client.end();
    }
  }

  // Stock-specific convenience methods
  subscribeToStockUpdates(symbols: string[]): void {
    symbols.forEach(symbol => {
      this.subscribe(`stocks/${symbol}/quote`);
      this.subscribe(`stocks/${symbol}/trades`);
    });
  }

  subscribeToMarketData(): void {
    this.subscribe('market/status');
    this.subscribe('market/indices');
    this.subscribe('market/news');
  }

  // Observable getters
  get connectionStatus(): Observable<ConnectionStatus> {
    return this.connectionStatus$.asObservable();
  }

  get messages(): Observable<MQTTMessage> {
    return this.messages$.asObservable();
  }

  get isConnected(): boolean {
    return this.connectionStatus$.value.isConnected;
  }

  get subscribedTopicsList(): string[] {
    return Array.from(this.subscribedTopics);
  }

  private parsePayload(payload: Buffer): any {
    try {
      const payloadString = payload.toString();
      return JSON.parse(payloadString);
    } catch {
      return payload.toString();
    }
  }

  private updateConnectionStatus(status: Partial<ConnectionStatus>): void {
    const currentStatus = this.connectionStatus$.value;
    this.connectionStatus$.next({ ...currentStatus, ...status });
  }

  ngOnDestroy(): void {
    this.disconnect();
    this.connectionStatus$.complete();
    this.messages$.complete();
  }
}
