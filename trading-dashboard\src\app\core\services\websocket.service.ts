import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { io, Socket } from 'socket.io-client';
import { 
  WebSocketMessage, 
  StockUpdateMessage, 
  MarketStatusMessage, 
  NewsUpdateMessage,
  SubscriptionMessage,
  ConnectionStatus 
} from '../../interfaces/websocket.interface';

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket: Socket | null = null;
  private connectionStatus$ = new BehaviorSubject<ConnectionStatus>({
    isConnected: false,
    reconnectAttempts: 0
  });
  
  private stockUpdates$ = new Subject<StockUpdateMessage>();
  private marketStatus$ = new Subject<MarketStatusMessage>();
  private newsUpdates$ = new Subject<NewsUpdateMessage>();
  private allMessages$ = new Subject<WebSocketMessage>();

  private readonly serverUrl = 'ws://localhost:3000'; // Will be configurable
  private subscribedSymbols: Set<string> = new Set();

  constructor() {
    this.initializeConnection();
  }

  private initializeConnection(): void {
    try {
      this.socket = io(this.serverUrl, {
        autoConnect: false,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 20000
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Failed to initialize WebSocket connection:', error);
      this.updateConnectionStatus({
        isConnected: false,
        reconnectAttempts: 0,
        error: 'Failed to initialize connection'
      });
    }
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.updateConnectionStatus({
        isConnected: true,
        lastConnected: new Date(),
        reconnectAttempts: 0
      });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.updateConnectionStatus({
        isConnected: false,
        error: reason
      });
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      const currentStatus = this.connectionStatus$.value;
      this.updateConnectionStatus({
        isConnected: false,
        reconnectAttempts: currentStatus.reconnectAttempts + 1,
        error: error.message
      });
    });

    this.socket.on('stock_update', (data: any) => {
      const message: StockUpdateMessage = {
        type: 'stock_update',
        data: {
          ...data,
          timestamp: new Date(data.timestamp)
        },
        timestamp: new Date()
      };
      this.stockUpdates$.next(message);
      this.allMessages$.next(message);
    });

    this.socket.on('market_status', (data: any) => {
      const message: MarketStatusMessage = {
        type: 'market_status',
        data: {
          ...data,
          nextOpenTime: data.nextOpenTime ? new Date(data.nextOpenTime) : undefined,
          nextCloseTime: data.nextCloseTime ? new Date(data.nextCloseTime) : undefined
        },
        timestamp: new Date()
      };
      this.marketStatus$.next(message);
      this.allMessages$.next(message);
    });

    this.socket.on('news_update', (data: any) => {
      const message: NewsUpdateMessage = {
        type: 'news_update',
        data: {
          ...data,
          publishedDate: new Date(data.publishedDate)
        },
        timestamp: new Date()
      };
      this.newsUpdates$.next(message);
      this.allMessages$.next(message);
    });
  }

  connect(): void {
    if (this.socket && !this.socket.connected) {
      this.socket.connect();
    }
  }

  disconnect(): void {
    if (this.socket && this.socket.connected) {
      this.socket.disconnect();
    }
  }

  subscribeToSymbols(symbols: string[], dataTypes: ('quotes' | 'news' | 'trades')[] = ['quotes']): void {
    if (!this.socket || !this.socket.connected) {
      console.warn('WebSocket not connected. Cannot subscribe to symbols.');
      return;
    }

    const message: SubscriptionMessage = {
      action: 'subscribe',
      symbols,
      dataTypes
    };

    this.socket.emit('subscribe', message);
    symbols.forEach(symbol => this.subscribedSymbols.add(symbol));
  }

  unsubscribeFromSymbols(symbols: string[]): void {
    if (!this.socket || !this.socket.connected) {
      console.warn('WebSocket not connected. Cannot unsubscribe from symbols.');
      return;
    }

    const message: SubscriptionMessage = {
      action: 'unsubscribe',
      symbols,
      dataTypes: ['quotes', 'news', 'trades']
    };

    this.socket.emit('unsubscribe', message);
    symbols.forEach(symbol => this.subscribedSymbols.delete(symbol));
  }

  // Observable getters
  get connectionStatus(): Observable<ConnectionStatus> {
    return this.connectionStatus$.asObservable();
  }

  get stockUpdates(): Observable<StockUpdateMessage> {
    return this.stockUpdates$.asObservable();
  }

  get marketStatus(): Observable<MarketStatusMessage> {
    return this.marketStatus$.asObservable();
  }

  get newsUpdates(): Observable<NewsUpdateMessage> {
    return this.newsUpdates$.asObservable();
  }

  get allMessages(): Observable<WebSocketMessage> {
    return this.allMessages$.asObservable();
  }

  get isConnected(): boolean {
    return this.connectionStatus$.value.isConnected;
  }

  get subscribedSymbolsList(): string[] {
    return Array.from(this.subscribedSymbols);
  }

  private updateConnectionStatus(status: Partial<ConnectionStatus>): void {
    const currentStatus = this.connectionStatus$.value;
    this.connectionStatus$.next({ ...currentStatus, ...status });
  }

  ngOnDestroy(): void {
    this.disconnect();
    this.connectionStatus$.complete();
    this.stockUpdates$.complete();
    this.marketStatus$.complete();
    this.newsUpdates$.complete();
    this.allMessages$.complete();
  }
}
