{"version": 3, "file": "FsaNodeReadStream.js", "sourceRoot": "", "sources": ["../../src/fsa-to-node/FsaNodeReadStream.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAClC,6CAA0C;AAC1C,yDAAsD;AAMtD,MAAa,iBAAkB,SAAQ,iBAAQ;IAO7C,YACqB,EAAa,EACb,MAAkC,EACrC,IAAY,EACT,OAA2B;QAE9C,KAAK,EAAE,CAAC;QALW,OAAE,GAAF,EAAE,CAAW;QACb,WAAM,GAAN,MAAM,CAA4B;QACrC,SAAI,GAAJ,IAAI,CAAQ;QACT,YAAO,GAAP,OAAO,CAAoB;QAVtC,gBAAW,GAAY,IAAI,CAAC;QAC5B,eAAU,GAAY,KAAK,CAAC;QAC5B,cAAS,GAAW,CAAC,CAAC;QACb,cAAS,GAAG,IAAA,yBAAW,EAAC,CAAC,CAAC,CAAC;QAC3B,aAAQ,GAAG,IAAI,aAAK,EAAqB,CAAC;QAS3D,MAAM;aACH,IAAI,CAAC,IAAI,CAAC,EAAE;YACX,IAAI,IAAI,CAAC,UAAU;gBAAE,OAAO;YAC5B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,SAAS;gBAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9D,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC;aACD,OAAO,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAC3B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,QAAQ;QACpB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAyB,KAAK,IAAI,EAAE;YAC7D,IAAI,IAAI,CAAC,UAAU;gBAAE,OAAO;YAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC7C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC;YACtC,IAAI,GAAG,GAAG,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;YAC1F,IAAI,GAAG,GAAG,MAAM,CAAC,UAAU;gBAAE,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC;YACrD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,SAAS;QACf,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,IAAI,CAAC,QAAQ,CAAC,OAAO;iBAClB,IAAI,CAAC,IAAI,CAAC,EAAE;gBACX,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE;oBAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;YACtB,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE,GAAE,CAAC,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IAED,6EAA6E;IAE7E,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,IAAW,OAAO;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,6EAA6E;IAE7E,KAAK;QACH,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAClB,CAAC,KAAiB,EAAE,EAAE;YACpB,IAAI,IAAI,CAAC,UAAU;gBAAE,OAAO;YAC5B,IAAI,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC;YAC/B,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClB,CAAC,EACD,KAAK,CAAC,EAAE;YACN,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AAtFD,8CAsFC"}