{"version": 3, "file": "NodeFileSystemFileHandle.js", "sourceRoot": "", "sources": ["../../src/node-to-fsa/NodeFileSystemFileHandle.ts"], "names": [], "mappings": ";;;AAAA,iEAA8D;AAC9D,qFAAkF;AAClF,iCAAwF;AACxF,yFAAsF;AAItF,MAAa,wBAAyB,SAAQ,2CAAoB;IAGhE,YACqB,EAAa,EAChB,MAAc,EAC9B,MAA+B,EAAE;QAEjC,GAAG,GAAG,IAAA,UAAS,EAAC,GAAG,CAAC,CAAC;QACrB,KAAK,CAAC,MAAM,EAAE,IAAA,eAAQ,EAAC,MAAM,EAAE,GAAG,CAAC,SAAU,CAAC,CAAC,CAAC;QAL7B,OAAE,GAAF,EAAE,CAAW;QAChB,WAAM,GAAN,MAAM,CAAQ;QAK9B,IAAI,CAAC,GAAG,GAAG,GAAqB,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,OAAO;QAClB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;YAClC,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,oFAAoF;YACpF,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAClF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,YAAY;gBAAE,MAAM,KAAK,CAAC;YAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,OAAO,CAAC;oBACb,KAAK,QAAQ;wBACX,MAAM,IAAA,yBAAkB,GAAE,CAAC;gBAC/B,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAW,sBAAsB;QAC/B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,iBAAiB;YAAE,OAAO,SAAS,CAAC;QAClD,OAAO,KAAK,IAAI,EAAE,CAAC,IAAI,+DAA8B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CACzB,EAAE,gBAAgB,GAAG,KAAK,KAA4B,EAAE,gBAAgB,EAAE,KAAK,EAAE;QAEjF,IAAA,qBAAc,EAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC9B,OAAO,IAAI,mEAAgC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;IACtF,CAAC;CACF;AA1DD,4DA0DC"}